# 环境安装配置指南

> **适用人群**：产品经理、测试人员
> 
> **支持系统**：Windows 10/11、macOS 10.15+

## 📋 快速导航

### 🎯 核心工具安装
- [GitLab配置](#gitlab-配置) - 代码版本管理（测试岗必备）
- [Node.js安装](#nodejs-安装) - JavaScript运行环境
- [Python安装](#python-安装) - Python运行环境
- [Trae IDE安装](#trae-ide-安装) - AI智能开发工具（测试岗必备）

### 🚀 快速开始
- [环境检查](#环境检查) - 验证安装是否成功
- [首次配置](#首次配置) - 完成基础配置
- [常见问题](#常见问题) - 故障排除指南

---

## 🔧 GitLab 配置

### Windows 用户

#### 1. 安装 Git
1. 访问 [Git官网](https://git-scm.com/download/win) 下载最新版本
2. 运行安装程序，使用默认设置即可
3. 安装完成后，打开命令提示符验证：
   ```cmd
   git --version
   ```

#### 2. 配置 GitLab
1. **设置用户信息**：
   ```cmd
   git config --global user.name "你的姓名"
   git config --global user.email "你的邮箱@company.com"
   ```

2. **生成SSH密钥**：
   ```cmd
   ssh-keygen -t rsa -b 4096 -C "你的邮箱@company.com"
   ```
   - 按回车使用默认路径
   - 设置密码（可选，建议设置）

3. **添加SSH密钥到GitLab**：
   - 复制公钥内容：
     ```cmd
     type %USERPROFILE%\.ssh\id_rsa.pub
     ```
   - 登录GitLab → 用户设置 → SSH密钥 → 粘贴公钥

### macOS 用户

#### 1. 安装 Git
Git通常已预装在macOS中，如需更新：
```bash
# 使用Homebrew安装（推荐）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
brew install git
```

#### 2. 配置 GitLab
1. **设置用户信息**：
   ```bash
   git config --global user.name "你的姓名"
   git config --global user.email "你的邮箱@company.com"
   ```

2. **生成SSH密钥**：
   ```bash
   ssh-keygen -t rsa -b 4096 -C "你的邮箱@company.com"
   ```

3. **添加SSH密钥到GitLab**：
   ```bash
   cat ~/.ssh/id_rsa.pub
   ```
   复制输出内容到GitLab SSH密钥设置

### GitLab 基础使用

#### 克隆项目
```bash
<NAME_EMAIL>:your-group/your-project.git
cd your-project
```

#### 基本工作流程
```bash
# 拉取最新代码
git pull origin main

# 创建新分支
git checkout -b feature/your-feature-name

# 提交更改
git add .
git commit -m "feat: 描述你的更改"

# 推送到远程
git push origin feature/your-feature-name
```

---

## 🟢 Node.js 安装

### Windows 用户

#### 1. 下载安装
1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载LTS版本（推荐18.x或20.x）
3. 运行安装程序，勾选"Add to PATH"选项

#### 2. 验证安装
```cmd
node --version
npm --version
```

#### 3. 配置npm镜像（可选，提升下载速度）
```cmd
npm config set registry https://registry.npmmirror.com
```

### macOS 用户

#### 方法1：官网下载（推荐新手）
1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载macOS安装包
3. 运行安装程序

#### 方法2：使用Homebrew
```bash
brew install node
```

#### 验证安装
```bash
node --version
npm --version
```

### Node.js 基础使用

#### 常用npm命令
```bash
# 初始化项目
npm init -y

# 安装依赖
npm install package-name

# 全局安装
npm install -g package-name

# 查看已安装包
npm list
```

---

## 🐍 Python 安装

### Windows 用户

#### 1. 下载安装
1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载Python 3.9+版本
3. 运行安装程序，**重要**：勾选"Add Python to PATH"

#### 2. 验证安装
```cmd
python --version
pip --version
```

#### 3. 配置pip镜像（可选）
```cmd
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
```

### macOS 用户

#### 方法1：官网下载
1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载macOS安装包
3. 运行安装程序

#### 方法2：使用Homebrew（推荐）
```bash
brew install python
```

#### 验证安装
```bash
python3 --version
pip3 --version
```

### Python 基础使用

#### 虚拟环境管理
```bash
# 创建虚拟环境
python -m venv myenv

# 激活虚拟环境
# Windows:
myenv\Scripts\activate
# macOS:
source myenv/bin/activate

# 安装包
pip install package-name

# 退出虚拟环境
deactivate
```

---

## 🤖 Trae IDE 安装

### 下载安装

#### Windows 用户
1. 访问 [Trae官网](https://www.trae.ai/)
2. 点击"Download"下载Windows版本
3. 运行安装程序，按提示完成安装
4. 首次启动需要注册账号

#### macOS 用户
1. 访问 [Trae官网](https://www.trae.ai/)
2. 下载macOS版本
3. 将应用拖拽到Applications文件夹
4. 首次启动可能需要在"系统偏好设置 → 安全性与隐私"中允许运行

### 基础配置

#### 1. 账号注册登录
- 使用邮箱注册
- 完成邮箱验证
- 登录Trae IDE

#### 2. MCP服务配置
在Trae IDE中配置测试所需的服务：

**设置路径**：Trae IDE → 设置 → MCP服务 → 添加服务

**Playwright服务配置**：
```json
{
  "mcpServers": {
    "Playwright": {
      "command": "npx",
      "args": ["-y", "@executeautomation/playwright-mcp-server"],
      "env": {}
    }
  }
}
```

#### 3. 智能体配置
配置专用的测试智能体：

**测试用例生成智能体**：
```
docs/tester/测试用例提示词.md 请使用文档中提示词完成测试用例文档编写，文档输出路径：docs/tester/test-cases
```

**测试执行智能体**：
```
docs/tester/测试用例执行提示词.md 请使用文档中提示词完成功能测试，并输出测试报告，文档输出路径：docs/tester/test-report
```

---

## ✅ 环境检查

### 一键检查脚本

#### Windows 检查脚本
创建 `check_env.bat` 文件：
```batch
@echo off
echo ========== 环境检查开始 ==========
echo.

echo 检查 Git...
git --version
if %errorlevel% neq 0 echo [错误] Git 未安装或未添加到PATH

echo.
echo 检查 Node.js...
node --version
if %errorlevel% neq 0 echo [错误] Node.js 未安装或未添加到PATH

echo.
echo 检查 npm...
npm --version
if %errorlevel% neq 0 echo [错误] npm 未安装或未添加到PATH

echo.
echo 检查 Python...
python --version
if %errorlevel% neq 0 echo [错误] Python 未安装或未添加到PATH

echo.
echo 检查 pip...
pip --version
if %errorlevel% neq 0 echo [错误] pip 未安装或未添加到PATH

echo.
echo ========== 环境检查完成 ==========
pause
```

#### macOS 检查脚本
创建 `check_env.sh` 文件：
```bash
#!/bin/bash
echo "========== 环境检查开始 =========="
echo

echo "检查 Git..."
if command -v git &> /dev/null; then
    git --version
else
    echo "[错误] Git 未安装"
fi

echo
echo "检查 Node.js..."
if command -v node &> /dev/null; then
    node --version
else
    echo "[错误] Node.js 未安装"
fi

echo
echo "检查 npm..."
if command -v npm &> /dev/null; then
    npm --version
else
    echo "[错误] npm 未安装"
fi

echo
echo "检查 Python..."
if command -v python3 &> /dev/null; then
    python3 --version
else
    echo "[错误] Python 未安装"
fi

echo
echo "检查 pip..."
if command -v pip3 &> /dev/null; then
    pip3 --version
else
    echo "[错误] pip 未安装"
fi

echo
echo "========== 环境检查完成 =========="
```

运行方式：
```bash
chmod +x check_env.sh
./check_env.sh
```

---

## 🔧 首次配置

### 1. 项目克隆
```bash
# 克隆项目到本地
git clone http://gitlab.funi.local/funisaasproject/funi-cloud-mi.git
cd [项目目录]
```

### 2. 依赖安装
```bash
# 如果项目有package.json
npm install

# 如果项目有requirements.txt
pip install -r requirements.txt
```

### 3. Trae IDE 项目配置
1. 打开Trae IDE
2. 选择"打开项目"
3. 选择克隆的项目目录
4. 等待项目索引完成

### 4. 测试环境验证
1. 在Trae IDE中新建对话
2. 选择测试智能体
3. 输入："请检查测试环境是否配置正确"
4. 确认所有服务正常运行

---

## ❓ 常见问题

### Git 相关问题

**Q: SSH连接GitLab失败**
```bash
# 测试SSH连接
ssh -T **************

# 如果失败，检查SSH密钥
ls -la ~/.ssh/
```

**A: 解决方案**
1. 确认SSH密钥已添加到GitLab
2. 检查SSH代理：
   ```bash
   eval "$(ssh-agent -s)"
   ssh-add ~/.ssh/id_rsa
   ```

### Node.js 相关问题

**Q: npm install 速度慢或失败**
```bash
# 切换到国内镜像
npm config set registry https://registry.npmmirror.com

# 清除缓存
npm cache clean --force
```

**Q: 权限错误（macOS/Linux）**
```bash
# 修复npm权限
sudo chown -R $(whoami) ~/.npm
```

### Python 相关问题

**Q: pip install 失败**
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple package-name
```

**Q: Python版本冲突**
```bash
# 使用虚拟环境
python -m venv project_env
source project_env/bin/activate  # macOS
project_env\Scripts\activate     # Windows
```

### Trae IDE 相关问题

**Q: MCP服务启动失败**
1. 确认Node.js版本 >= 16.0.0
2. 重新安装MCP服务：
   ```bash
   npm install -g @executeautomation/playwright-mcp-server
   ```

**Q: 智能体无响应**
1. 检查网络连接
2. 重启Trae IDE
3. 清除应用缓存

---

## 📞 获取帮助

### 外部资源
- [Git官方文档](https://git-scm.com/doc)
- [Node.js官方文档](https://nodejs.org/docs/)
- [Python官方文档](https://docs.python.org/)
- [Trae官方文档](https://docs.trae.ai/)

---

## 🎉 安装完成

恭喜！你已经完成了所有环境的安装和配置。现在可以：

1. **开始使用测试工具**：参考 [测试用例生成指南](./tester/test-case-generation-prompts.md)
2. **学习产品工具**：查看 [产品管理工具](./pm/)
3. **加入团队协作**：使用GitLab进行版本控制

**下一步**：建议先阅读 [快速开始指南](./tester/test-case-generation-prompts.md#快速开始) 来熟悉整个工作流程。

---

## 📚 附录

### A. 系统要求

#### 最低配置要求
| 组件 | Windows | macOS |
|------|---------|-------|
| **操作系统** | Windows 10 (1903+) | macOS 10.15+ |
| **内存** | 8GB RAM | 8GB RAM |
| **存储** | 10GB 可用空间 | 10GB 可用空间 |
| **网络** | 稳定的互联网连接 | 稳定的互联网连接 |

#### 推荐配置
| 组件 | Windows | macOS |
|------|---------|-------|
| **操作系统** | Windows 11 | macOS 12+ |
| **内存** | 16GB RAM | 16GB RAM |
| **存储** | 20GB+ SSD | 20GB+ SSD |
| **处理器** | Intel i5/AMD Ryzen 5+ | Apple M1/Intel i5+ |

### B. 版本兼容性

#### 推荐版本组合
```
Git: 2.40+
Node.js: 18.x LTS 或 20.x LTS
Python: 3.9+ (推荐 3.11)
Trae IDE: 最新版本
```

#### 版本检查命令
```bash
# 一键检查所有版本
echo "=== 版本信息 ==="
echo "Git: $(git --version)"
echo "Node.js: $(node --version)"
echo "npm: $(npm --version)"
echo "Python: $(python --version 2>/dev/null || python3 --version)"
echo "pip: $(pip --version 2>/dev/null || pip3 --version)"
```

### C. 网络配置

#### 企业网络配置
如果在企业网络环境中，可能需要配置代理：

**Git代理配置**：
```bash
# HTTP代理
git config --global http.proxy http://proxy.company.com:8080
git config --global https.proxy https://proxy.company.com:8080

# 取消代理
git config --global --unset http.proxy
git config --global --unset https.proxy
```

**npm代理配置**：
```bash
# 设置代理
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy https://proxy.company.com:8080

# 取消代理
npm config delete proxy
npm config delete https-proxy
```

**pip代理配置**：
```bash
# 临时使用代理
pip install --proxy http://proxy.company.com:8080 package-name

# 永久配置（创建 ~/.pip/pip.conf 或 %APPDATA%\pip\pip.ini）
[global]
proxy = http://proxy.company.com:8080
```

### D. 安全配置

#### SSH密钥管理最佳实践
1. **使用强密码保护私钥**
2. **定期轮换SSH密钥**（建议每6个月）
3. **不同项目使用不同密钥**：
   ```bash
   # 生成项目专用密钥
   ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa_project -C "<EMAIL>"

   # 配置SSH config
   cat >> ~/.ssh/config << EOF
   Host gitlab-project
       HostName gitlab.com
       User git
       IdentityFile ~/.ssh/id_rsa_project
   EOF
   ```

#### 凭据管理
- **Windows**：使用Windows凭据管理器
- **macOS**：使用Keychain Access
- **避免在代码中硬编码密码**

### E. 性能优化

#### Git性能优化
```bash
# 启用文件系统缓存
git config --global core.preloadindex true
git config --global core.fscache true

# 增加缓存大小
git config --global core.packedGitLimit 512m
git config --global core.packedGitWindowSize 512m
```

#### Node.js性能优化
```bash
# 增加内存限制
export NODE_OPTIONS="--max-old-space-size=4096"

# 使用更快的包管理器
npm install -g pnpm
# 或
npm install -g yarn
```

#### Python性能优化
```bash
# 使用更快的包安装
pip install --upgrade pip setuptools wheel

# 启用pip缓存
pip config set global.cache-dir ~/.pip/cache
```

### F. 备份与恢复

#### 配置备份
定期备份重要配置文件：

**Windows**：
```cmd
# 备份Git配置
copy %USERPROFILE%\.gitconfig backup\
copy %USERPROFILE%\.ssh\* backup\ssh\

# 备份npm配置
copy %USERPROFILE%\.npmrc backup\
```

**macOS**：
```bash
# 备份Git配置
cp ~/.gitconfig backup/
cp -r ~/.ssh backup/

# 备份npm配置
cp ~/.npmrc backup/
```

#### 快速恢复脚本
创建恢复脚本以便快速重新配置环境：

```bash
#!/bin/bash
# restore_env.sh

echo "开始恢复环境配置..."

# 恢复Git配置
cp backup/.gitconfig ~/
cp -r backup/.ssh ~/
chmod 600 ~/.ssh/id_rsa

# 恢复npm配置
cp backup/.npmrc ~/

# 重新安装全局包
npm install -g @executeautomation/playwright-mcp-server

echo "环境配置恢复完成！"
```

### G. 故障排除进阶

#### 日志收集
当遇到问题时，收集以下日志信息：

```bash
# 系统信息
uname -a                    # 系统版本
echo $PATH                  # 环境变量

# Git诊断
git config --list          # Git配置
git remote -v              # 远程仓库

# Node.js诊断
npm config list            # npm配置
npm ls -g --depth=0        # 全局包

# Python诊断
pip list                   # 已安装包
python -m site             # Python路径
```

#### 常见错误代码
| 错误代码 | 含义 | 解决方案 |
|----------|------|----------|
| `128` | Git操作失败 | 检查仓库权限和网络 |
| `EACCES` | 权限错误 | 修复文件权限 |
| `ENOTFOUND` | 网络连接失败 | 检查网络和代理设置 |
| `MODULE_NOT_FOUND` | 模块未找到 | 重新安装依赖 |

---

## 📝 更新日志

### v1.0.0 (2024-08-20)
- ✅ 初始版本发布
- ✅ 支持Windows和macOS
- ✅ 包含Git、Node.js、Python、Trae IDE完整安装指南
- ✅ 提供故障排除和最佳实践

---

**📞 文档维护**：如发现问题或需要更新，请在GitLab中提交Issue或联系技术团队。
