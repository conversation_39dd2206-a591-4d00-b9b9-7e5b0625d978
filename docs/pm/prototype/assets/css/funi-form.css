/* General form styling */
:root {
    --funi-label-background-color: #f8f8f8;
    --funi-label-background-color-dark: #333;
}

.form-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: stretch;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
}

.container-content {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
    background-color: var(--funi-background-color-light);
}

.funi-form {
    /* background-color: var(--funi-background-color-light); */
    padding: 0;
    border-radius: 8px;
    margin-bottom: 20px;
    position: relative;

}

.funi-form::after,
.funi-form::before {
    display: inline-block;
    content: "";
    width: 1px;
    height: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 10;
    background-color: var(--funi-border-color-light);
}

.funi-form::before {
    left: 0;
}

.funi-form::after {
    right: 0;
}

/* Styles for the new detail page header */
.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: var(--funi-background-color-light);
    /* border-bottom: 1px solid var(--funi-border-color-lighter); */
    margin-bottom: 1em;
    border-radius: 4px;
}

.detail-header .header-left .header-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
}

.detail-header .header-right .approval-info {
    font-size: 14px;
    color: #666;
}

.detail-header .header-right .approval-info span {
    margin-left: 20px;
}

/* Styles for the new tabs */
.funi-tabs {
    display: flex;
    border-bottom: 2px solid #eee;
    padding: 16px 10px 0;
    background-color: var(--funi-background-color-light);
    gap: 2em;
    border-radius: 4px 4px 0 0;
}

.funi-tabs .tab-item {
    padding: 0;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.funi-tabs .tab-item:hover {
    color: var(--funi-primary-color);
}

.funi-tabs .tab-item.active {
    color: var(--funi-primary-color);
    border-bottom-color: var(--funi-primary-color);
    /* font-weight: bold; */
}

/* Ensure only active tab content is visible */
.form-step-content {
    display: none;
}

.form-step-content.active {
    display: block;
}

/* Styling for display-value to maintain form-item-value appearance */
.form-item-value .display-value {
    /* padding: 0 9px; */
    background-color: var(--funi-input-background-color);
    /* Use existing input background */
    color: var(--funi-text-color-regular);
    min-height: 32px;
    /* Match input/select height */
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    /* Ensure it takes full width of its container */
}

/* Styles for the 3-column grid layout */
.form-grid {
    --form-grid-column: 3;
    display: grid;
    grid-template-columns: repeat(var(--form-grid-column), 1fr);
    gap: 0;
    padding: 0;
    /* background-color: var(--funi-background-color-light); */
    box-shadow: none;
    position: relative;

}

.form-grid::before,
.form-grid::after {
    display: inline-block;
    content: "";
    height: 1px;
    position: absolute;
    left: 0;
    right: 0;
    background-color: var(--funi-border-color-light);
}

.form-grid::before {
    top: 0;
}

.form-grid::after {
    bottom: 0;
}

.form-section-title {
    grid-column: 1 / -1;

    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--funi-primary-color);

    /* Adjusted padding-bottom */
    margin-top: 20px;
    position: relative;
    /* Added for pseudo-element positioning */
    padding-left: 15px;
    display: flex;
    align-items: center;
    border-left: 5px solid var(--funi-primary-color);
}



.form-section-title:first-of-type {
    margin-top: 0;
}

.form-item-row {
    display: flex;
    align-items: stretch;
    gap: 0;
    border-right: 1px solid var(--funi-border-color-light);
    border-bottom: 1px solid var(--funi-border-color-light);
    position: relative;
}

.form-item-row::before {
    content: "";
    display: inline-block;
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--funi-border-color-light);
    z-index: 1;
}

.form-item-row:nth-child(1):not(.form-item-full-width)::before,
.form-item-row:nth-child(2):not(.form-item-full-width)::before,
.form-item-row:nth-child(3):not(.form-item-full-width)::before {
    top: 0;
}

.form-item-row.form-item-full-width {
    grid-column: 1 / -1;
    /* Span all columns for full-width items */
}

.form-item-row label {
    flex-shrink: 0;
    width: 180px;
    /* Adjusted width for labels */
    justify-content: flex-end;
    /* Align label text to the right */
    font-size: 14px;
    background-color: var(--funi-background-color-base);
    padding: 5px 4px;
    /* Adjusted padding */
    border-radius: 0;
    /* Removed border-radius */
    border-right: 1px solid var(--funi-border-color-light);
    /* Re-added right border for label */
    color: var(--funi-text-color-regular);
    display: flex;
    align-items: center;
    gap: 5px;
    box-sizing: border-box;
    /* Include padding in the width */
}

.form-item-row label.required::before {
    content: "*";
    color: var(--funi-danger-color);

}

.form-item-value>*:first-child {
    flex-grow: 1;
}



.form-item-row .form-item-value {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 4px;
    flex: 1
}

/* Dark theme support for label background */
/* html.dark-theme .form-item-row label {
    background-color: var(--funi-label-background-color-dark);
} */

.input-and-tip-wrapper {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 10px 20px;
    /* Adjusted padding */
}

.form-item-row input[type="text"],
.form-item-row input[type="number"],
.form-item-row input[type="date"],
.form-item-row select,
.form-item-row textarea {
    width: 100%;
    /* Take full width of its wrapper */
    padding: 8px 12px;
    border: 1px solid var(--funi-border-color-base);
    border-radius: 4px;
    box-sizing: border-box;
    background-color: var(--funi-input-background-color);
    color: var(--funi-text-color-regular);
}

.form-item-row input[type="file"] {
    padding: 5px 0;
}

.form-item-row input:focus,
.form-item-row select:focus,
.form-item-row textarea:focus {
    outline: none;
    border-color: var(--funi-primary-color);
}

.form-item-row textarea {
    min-height: 80px;
    resize: vertical;
}

.form-item-tip {
    font-size: 0.85em;
    color: var(--funi-text-color-secondary);
    margin-top: 2px;
}



.form-actions {
    grid-column: 1 / -1;
    /* Span all columns */
    display: flex;
    justify-content: flex-end;
    /* gap: 10px; */
    padding: 5px 10px;
    background-color: var(--funi-background-color-light);
}

/* Buttons - Reusing .button styles from list.css or funi-components.css */
.form-actions .button {
    margin-left: 10px;
    padding: 5px 10px;
    border-radius: 4px;
    border: 1px solid var(--funi-border-color-base);
}

.button.primary {
    background-color: var(--funi-primary-color);
    color: #FFFFFF;
    border: 1px solid var(--funi-primary-color);
}



/* Detail page specific styles (reusing form-item-row for consistency) */
.detail-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 20px;
    background-color: var(--funi-background-color-light);
    border-radius: 8px;
    box-shadow: var(--funi-box-shadow-base);
}

.detail-section-title {
    grid-column: 1 / -1;
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--funi-text-color-primary);
    border-bottom: 1px solid var(--funi-border-color-lighter);
    padding-bottom: 10px;
    margin-top: 20px;
}

.detail-section-title:first-of-type {
    margin-top: 0;
}

.detail-item-full-width {
    grid-column: 1 / -1;
}

.detail-item-value {
    /* For the span displaying the value */
    color: var(--funi-text-color-regular);
    padding: 8px 0;
    flex-grow: 1;
}

/* Timeline styles */
.timeline-container {
    position: relative;
    padding: 20px;
    background-color: var(--funi-background-color-light);
    border-radius: 8px;
    box-shadow: var(--funi-box-shadow-base);
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--funi-border-color-lighter);
}

.timeline-item {
    position: relative;
    margin-left: 50px;
    padding-bottom: 20px;
}

.timeline-dot {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    background-color: var(--funi-primary-color);
    border-radius: 50%;
    border: 2px solid var(--funi-background-color-light);
    z-index: 1;
}

.timeline-content {
    background-color: var(--funi-background-color-base);
    padding: 15px;
    border-radius: 8px;
    box-shadow: var(--funi-box-shadow-base);
    color: var(--funi-text-color-regular);
}

.timeline-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--funi-text-color-primary);
}

.timeline-meta span {
    display: block;
    font-size: 0.9em;
    color: var(--funi-text-color-secondary);
}

.container {
    padding: 20px;
    /* Added padding to the container */
    min-height: 100%;
    /* Set minimum height to 100% */
}

/* Step component styles */
.form-step-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background-color: #ffffff;
    border-bottom: 1px solid var(--funi-border-color-light);
    border-radius: 8px;
    gap: 20px;
}

.form-step {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: var(--funi-text-color-secondary);
    transition: color 0.3s ease;
    gap: 8px;
    position: relative;
}

.form-step:hover {
    color: var(--funi-primary-color);
}

.form-step.active {
    color: var(--funi-primary-color);
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #f0f0f0;
    border: 2px solid #d9d9d9;
    color: var(--funi-text-color-secondary);
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-step.active .step-number {
    background-color: var(--funi-primary-color);
    border-color: var(--funi-primary-color);
    color: #FFFFFF;
}

.step-title {
    font-size: 14px;
    white-space: nowrap;
    font-weight: 400;
    margin-top: 4px;
}

.form-step.active .step-title {
    font-weight: 500;
    color: var(--funi-primary-color);
}

.funi-step-icon {
    font-size: 16px;
    color: #d9d9d9;
    margin: 0 10px;
}