@echo off
chcp 65001 >nul
title Funi原型系统服务器 v2.0

:: 设置颜色和样式
color 0A

echo.
echo ================================================================
echo                🚀 Funi原型系统服务器 v2.0
echo ================================================================
echo.
echo 📅 启动时间: %date% %time%
echo 📁 当前目录: %CD%
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"

:: 检查必要文件
if not exist "index.html" (
    echo ❌ 错误: 未找到 index.html 文件
    echo 💡 请确保在正确的目录中运行此脚本
    echo.
    pause
    exit /b 1
)

echo ✅ 文件检查通过
echo.
echo 🔍 检查Python环境...

:: 检查Python环境
set PYTHON_CMD=
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    echo ✅ 找到Python
) else (
    python3 --version >nul 2>&1
    if %errorlevel% equ 0 (
        set PYTHON_CMD=python3
        echo ✅ 找到Python3
    ) else (
        py --version >nul 2>&1
        if %errorlevel% equ 0 (
            set PYTHON_CMD=py
            echo ✅ 找到Python (py命令)
        ) else (
            echo ❌ 未找到Python环境
            echo.
            echo 💡 请安装Python后重试:
            echo    https://www.python.org/downloads/
            echo.
            echo 📋 安装步骤:
            echo    1. 访问上述网址下载Python
            echo    2. 安装时勾选 "Add Python to PATH"
            echo    3. 重新运行此脚本
            echo.
            pause
            exit /b 1
        )
    )
)

:: 显示Python版本信息
echo 📋 Python版本信息:
%PYTHON_CMD% --version
echo.

echo 🚀 启动Funi原型系统服务器...
echo ================================================================
echo 💡 提示:
echo    - 服务器启动后会自动打开浏览器
echo    - 如果浏览器未自动打开，请手动访问显示的地址
echo    - 按 Ctrl+C 停止服务器
echo    - 修改文件后刷新浏览器即可看到更新
echo ================================================================
echo.

:: 启动Python服务器
%PYTHON_CMD% start-server.py %1

:: 如果Python脚本失败，尝试备用方案
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  Python脚本启动失败，尝试使用内置HTTP服务器...
    echo.
    
    :: 尝试不同的端口
    set /a PORT=8080
    :retry_port
    echo 🔄 尝试端口 %PORT%...
    %PYTHON_CMD% -m http.server %PORT% 2>nul
    if %errorlevel% neq 0 (
        set /a PORT+=1
        if %PORT% lss 8090 goto retry_port
        echo ❌ 无法启动服务器，所有端口都被占用
    )
)

echo.
echo 🛑 服务器已停止
echo 感谢使用 Funi原型系统! 👋
echo.
pause
