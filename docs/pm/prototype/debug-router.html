<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由调试页面</title>
</head>
<body>
    <h1>路由调试页面</h1>
    <p>请打开浏览器控制台查看调试信息</p>
    
    <script>
        console.log('=== 路由调试信息 ===');
        console.log('当前URL:', window.location.href);
        console.log('当前hash:', window.location.hash);
        console.log('是否在iframe中:', window.top !== window);
        console.log('父窗口路径:', window.top ? window.top.location.pathname : 'N/A');
        console.log('当前窗口路径:', window.location.pathname);
        
        // 检查addNew函数是否存在
        console.log('addNew函数是否存在:', typeof window.addNew);
        if (typeof window.addNew === 'function') {
            console.log('addNew函数定义:', window.addNew.toString());
        }
        
        // 检查新建按钮
        setTimeout(() => {
            const newButton = document.querySelector('button[onclick="addNew()"]');
            console.log('新建按钮元素:', newButton);
            if (newButton) {
                console.log('按钮onclick属性:', newButton.getAttribute('onclick'));
                console.log('按钮事件监听器:', getEventListeners ? getEventListeners(newButton) : '需要在Chrome DevTools中查看');
            }
            
            // 尝试手动调用addNew
            if (typeof window.addNew === 'function') {
                console.log('尝试手动调用addNew函数...');
                try {
                    window.addNew();
                } catch (e) {
                    console.error('调用addNew函数出错:', e);
                }
            }
        }, 2000);
        
        // 监听hash变化
        window.addEventListener('hashchange', () => {
            console.log('Hash变化:', window.location.hash);
        });
    </script>
</body>
</html>