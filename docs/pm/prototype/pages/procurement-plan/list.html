<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 列表</title>
    <!-- 注意：在路由环境下，CSS文件应在主页面(index.html)中统一加载 -->
    <!-- 如果直接访问此页面，请取消注释以下CSS链接 -->
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/funi-list.css" />
    <style>
        /*
        数据表格链接样式规范：
        1. 数据列可点击文本：使用 <span onclick="..." class="table-link"> 避免浏览器默认样式冲突
        2. 操作列链接：使用 <a> 标签但添加 :not(.table-link) 选择器
        3. 统一使用主题色变量确保一致性
        */
        .data-table .table-link {
            color: var(--funi-primary-color, #007FFF) !important;
            cursor: pointer !important;
            text-decoration: none !important;
            border: none !important;
            outline: none !important;
            font-weight: normal !important;
            display: inline !important;
            background: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        .data-table .table-link:hover {
            color: var(--funi-primary-dark-color, #337ecc) !important;
            text-decoration: none !important;
        }

        .data-table .table-link:active,
        .data-table .table-link:focus {
            color: var(--funi-primary-color, #007FFF) !important;
            text-decoration: none !important;
            outline: none !important;
        }

        /* 操作列链接样式 - 仍使用a标签但确保主题色 */
        .data-table tbody td a:not(.table-link),
        .data-table td a:not(.table-link) {
            color: var(--funi-primary-color, #007FFF) !important;
            text-decoration: none !important;
            cursor: pointer !important;
        }
        .data-table tbody td a:not(.table-link):hover,
        .data-table td a:not(.table-link):hover {
            color: var(--funi-primary-dark-color, #337ecc) !important;
            text-decoration: none !important;
        }
    </style>
</head>

<body>
    <div id="app" class="container">
        <div class="container-header">
            <!-- 1. 头部Tab切换 -->
            <div class="tabs">
                <div class="tab-item active" data-tab="all">全部</div>
                <div class="tab-item" data-tab="pending">待办</div>
                <div class="tab-item" data-tab="done">已办</div>
            </div>

            <!-- 2. 搜索区域 -->
            <div class="search-area collapsed">
                <form class="search-form">
                    <div class="search-form-item">
                        <label for="planProjectName">计划项目名称:</label>
                        <input type="text" id="planProjectName" name="planProjectName" placeholder="请输入">
                    </div>
                    <div class="search-form-item">
                        <label for="auditStatus">审核状态:</label>
                        <select id="auditStatus" name="auditStatus">
                            <option value="">请选择</option>
                            <option value="待审核">待审核</option>
                            <option value="审核通过">审核通过</option>
                            <option value="审核驳回">审核驳回</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="procurementType">采购类型:</label>
                        <select id="procurementType" name="procurementType">
                            <option value="">请选择</option>
                            <option value="施工">施工</option>
                            <option value="货物">货物</option>
                            <option value="服务">服务</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="procurementMethod">采购方式:</label>
                        <select id="procurementMethod" name="procurementMethod">
                            <option value="">请选择</option>
                            <option value="公告比选">公告比选</option>
                            <option value="邀请比选">邀请比选</option>
                            <option value="竞争性磋商">竞争性磋商</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="询价择优">询价择优</option>
                            <option value="单一来源">单一来源</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="biddingCategory">招标类别:</label>
                        <select id="biddingCategory" name="biddingCategory">
                            <option value="">请选择</option>
                            <option value="工程建设">工程建设</option>
                            <option value="货物采购">货物采购</option>
                            <option value="服务采购">服务采购</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="projectType">项目类型:</label>
                        <select id="projectType" name="projectType">
                            <option value="">请选择</option>
                            <option value="基建项目">基建项目</option>
                            <option value="技改项目">技改项目</option>
                            <option value="维修项目">维修项目</option>
                            <option value="其他项目">其他项目</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="projectOwner">项目业主:</label>
                        <input type="text" id="projectOwner" name="projectOwner" placeholder="请输入">
                    </div>
                    <div class="search-form-item full-width">
                        <label for="createTimeStart">创建时间:</label>
                        <div class="date-range-picker">
                            <input type="date" id="createTimeStart" name="createTimeStart">
                            <span>~</span>
                            <input type="date" id="createTimeEnd" name="createTimeEnd">
                        </div>
                    </div>
                    <div class="search-form-item search-buttons-item">
                        <button type="button" class="button primary" id="queryButton">查询</button>
                        <button type="button" class="button" id="resetButton">重置</button>
                        <button type="button" class="button text" id="toggleCollapseButton">高级查询</button>
                    </div>
                </form>
            </div>
        </div>
        <div class="container-table">
            <!-- 操作按钮区域 -->
            <div class="action-buttons">
                <button class="button primary" onclick="addNew()">新建</button>
            </div>
            <!-- 3. 列表区域 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>计划编号</th>
                            <th>计划项目名称</th>
                            <th>审核状态</th>
                            <th>采购类型</th>
                            <th>采购方式</th>
                            <th>招标类别</th>
                            <th>采购预算金额（万元）</th>
                            <th>项目类型</th>
                            <th>项目业主</th>
                            <th>项目经办人</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Table rows will be inserted here by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- 分页组件 -->
            <div class="pagination-container">
                <span>总共 <span id="totalItems">0</span> 条</span>
                <select id="pageSizeSelect">
                    <option value="10">10 条/页</option>
                    <option value="20">20 条/页</option>
                    <option value="50">50 条/页</option>
                    <option value="100">100 条/页</option>
                </select>
                <div class="page-buttons">
                    <button id="prevPageButton" disabled>上一页</button>
                    <span id="currentPageSpan">1</span>
                    <button id="nextPageButton">下一页</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        // Global navigation functions - must be defined before DOMContentLoaded
        window.viewDetail = function(id) {
            console.log('viewDetail called with id:', id);
            try {
                // 检查是否在路由系统的iframe中运行
                const isInRouterIframe = window.top && window.top !== window && 
                    window.top.location.pathname.includes('/prototype/');
                console.log('viewDetail isInRouterIframe:', isInRouterIframe);
                
                if (isInRouterIframe) {
                    // 路由模式：通过父窗口的hash路由跳转
                    console.log('Using router navigation for viewDetail');
                    window.top.location.hash = `#/procurement-plan/detail-review?id=${id}`;
                } else {
                    // 独立页面模式：直接跳转到相对路径
                    console.log('Using direct navigation for viewDetail');
                    window.location.href = `./detail-review.html?id=${id}`;
                }
            } catch (e) {
                console.error('Navigation error:', e);
                // 降级处理：直接跳转
                window.location.href = `./detail-review.html?id=${id}`;
            }
        };

        window.editItem = function(id) {
            console.log('editItem called with id:', id);
            try {
                // 检查是否在路由系统的iframe中运行
                const isInRouterIframe = window.top && window.top !== window && 
                    window.top.location.pathname.includes('/prototype/');
                console.log('editItem isInRouterIframe:', isInRouterIframe);
                
                if (isInRouterIframe) {
                    // 路由模式：通过父窗口的hash路由跳转
                    console.log('Using router navigation for editItem');
                    window.top.location.hash = `#/procurement-plan/add-edit?id=${id}`;
                } else {
                    // 独立页面模式：直接跳转到相对路径
                    console.log('Using direct navigation for editItem');
                    window.location.href = `./add-edit.html?id=${id}`;
                }
            } catch (e) {
                console.error('Navigation error:', e);
                // 降级处理：直接跳转
                window.location.href = `./add-edit.html?id=${id}`;
            }
        };

        window.addNew = function() {
            console.log('addNew called');
            try {
                // 检查是否在路由系统的iframe中运行
                const isInRouterIframe = window.top && window.top !== window && 
                    window.top.location.pathname.includes('/prototype/');
                console.log('isInRouterIframe:', isInRouterIframe);
                console.log('window.top.location.pathname:', window.top ? window.top.location.pathname : 'N/A');
                console.log('window.location.pathname:', window.location.pathname);
                
                if (isInRouterIframe) {
                    // 路由模式：通过父窗口的hash路由跳转
                    console.log('Using router navigation');
                    window.top.location.hash = `#/procurement-plan/add-edit`;
                } else {
                    // 独立页面模式：直接跳转到相对路径
                    console.log('Using direct navigation');
                    window.location.href = `./add-edit.html`;
                }
            } catch (e) {
                console.error('Navigation error:', e);
                // 降级处理：直接跳转
                window.location.href = `./add-edit.html`;
            }
        };

        document.addEventListener('DOMContentLoaded', () => {
            const tabs = document.querySelectorAll('.tab-item');
            const searchForm = document.querySelector('.search-form');
            const queryButton = document.getElementById('queryButton');
            const resetButton = document.getElementById('resetButton');
            const toggleCollapseButton = document.getElementById('toggleCollapseButton');
            const tableBody = document.getElementById('tableBody');
            const pageSizeSelect = document.getElementById('pageSizeSelect');
            const prevPageButton = document.getElementById('prevPageButton');
            const nextPageButton = document.getElementById('nextPageButton');
            const currentPageSpan = document.getElementById('currentPageSpan');
            const totalItemsSpan = document.getElementById('totalItems');

            // 检查必需的元素是否存在
            if (!tableBody || !pageSizeSelect || !queryButton || !resetButton || !toggleCollapseButton) {
                console.error('Required elements not found');
                return;
            }

            let activeTab = 'all';
            let isCollapsed = true;
            let currentPage = 1;
            let pageSize = parseInt(pageSizeSelect.value) || 10;

            // Initial state for the search area
            const searchArea = document.querySelector('.search-area');
            if (searchArea && toggleCollapseButton) {
                if (isCollapsed) {
                    searchArea.classList.add('collapsed');
                    toggleCollapseButton.textContent = '高级查询';
                } else {
                    searchArea.classList.remove('collapsed');
                    toggleCollapseButton.textContent = '收起';
                }
            }

            const allTableData = [
                {
                    planNumber: '***********-001',
                    planProjectName: '办公楼装修改造工程',
                    auditStatus: '审核通过',
                    procurementType: '施工',
                    procurementMethod: '公告比选',
                    biddingCategory: '工程建设',
                    budgetAmount: '500.00',
                    projectType: '基建项目',
                    projectOwner: '集团本部',
                    projectHandler: '张三',
                    createTime: '2024-01-15'
                },
                {
                    planNumber: '***********-002',
                    planProjectName: '信息化设备采购项目',
                    auditStatus: '待审核',
                    procurementType: '货物',
                    procurementMethod: '邀请比选',
                    biddingCategory: '货物采购',
                    budgetAmount: '300.00',
                    projectType: '技改项目',
                    projectOwner: '信息技术部',
                    projectHandler: '李四',
                    createTime: '2024-01-16'
                },
                {
                    planNumber: '***********-003',
                    planProjectName: '物业管理服务采购',
                    auditStatus: '审核通过',
                    procurementType: '服务',
                    procurementMethod: '竞争性磋商',
                    biddingCategory: '服务采购',
                    budgetAmount: '200.00',
                    projectType: '其他项目',
                    projectOwner: '行政管理部',
                    projectHandler: '王五',
                    createTime: '2024-01-17'
                },
                {
                    planNumber: '***********-004',
                    planProjectName: '生产设备维修保养',
                    auditStatus: '审核驳回',
                    procurementType: '服务',
                    procurementMethod: '询价择优',
                    biddingCategory: '服务采购',
                    budgetAmount: '150.00',
                    projectType: '维修项目',
                    projectOwner: '生产部',
                    projectHandler: '赵六',
                    createTime: '2024-01-18'
                },
                {
                    planNumber: '***********-005',
                    planProjectName: '安全监控系统升级',
                    auditStatus: '待审核',
                    procurementType: '货物',
                    procurementMethod: '公告比选',
                    biddingCategory: '货物采购',
                    budgetAmount: '400.00',
                    projectType: '技改项目',
                    projectOwner: '安全保卫部',
                    projectHandler: '钱七',
                    createTime: '2024-01-19'
                },
                {
                    planNumber: '***********-006',
                    planProjectName: '员工培训服务采购',
                    auditStatus: '审核通过',
                    procurementType: '服务',
                    procurementMethod: '单一来源',
                    biddingCategory: '服务采购',
                    budgetAmount: '80.00',
                    projectType: '其他项目',
                    projectOwner: '人力资源部',
                    projectHandler: '孙八',
                    createTime: '2024-01-20'
                },
                {
                    planNumber: '***********-007',
                    planProjectName: '车辆采购项目',
                    auditStatus: '审核通过',
                    procurementType: '货物',
                    procurementMethod: '竞争性谈判',
                    biddingCategory: '货物采购',
                    budgetAmount: '600.00',
                    projectType: '基建项目',
                    projectOwner: '集团本部',
                    projectHandler: '周九',
                    createTime: '2024-01-21'
                },
                {
                    planNumber: '***********-008',
                    planProjectName: '食堂承包服务',
                    auditStatus: '待审核',
                    procurementType: '服务',
                    procurementMethod: '公告比选',
                    biddingCategory: '服务采购',
                    budgetAmount: '120.00',
                    projectType: '其他项目',
                    projectOwner: '后勤保障部',
                    projectHandler: '吴十',
                    createTime: '2024-01-22'
                }
            ];

            let filteredTableData = [...allTableData];

            const renderTable = () => {
                tableBody.innerHTML = '';
                const start = (currentPage - 1) * pageSize;
                const end = start + pageSize;
                const paginatedData = filteredTableData.slice(start, end);

                paginatedData.forEach(rowData => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${rowData.planNumber}</td>
                        <td><span onclick="viewDetail('${rowData.planNumber}')" class="table-link">${rowData.planProjectName}</span></td>
                        <td>${rowData.auditStatus}</td>
                        <td>${rowData.procurementType}</td>
                        <td>${rowData.procurementMethod}</td>
                        <td>${rowData.biddingCategory}</td>
                        <td>${rowData.budgetAmount}</td>
                        <td>${rowData.projectType}</td>
                        <td>${rowData.projectOwner}</td>
                        <td>${rowData.projectHandler}</td>
                        <td>${rowData.createTime}</td>
                        <td>
                            <button type="button" class="button text" data-action="view" data-id="${rowData.planNumber}">详情</button>
                            <button type="button" class="button text" data-action="edit" data-id="${rowData.planNumber}">编辑</button>
                            <button type="button" class="button text" data-action="delete" data-id="${rowData.planNumber}">删除</button>
                            <button type="button" class="button text" data-action="audit" data-id="${rowData.planNumber}">审核</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });

                totalItemsSpan.textContent = filteredTableData.length;
                currentPageSpan.textContent = currentPage;
                prevPageButton.disabled = currentPage === 1;
                nextPageButton.disabled = currentPage * pageSize >= filteredTableData.length;
            };

            const handleAction = (event) => {
                console.log('handleAction triggered', event.target);
                const button = event.target;
                console.log('Button tagName:', button.tagName, 'Action:', button.dataset.action, 'ID:', button.dataset.id);
                
                if (button.tagName === 'BUTTON' && button.dataset.action) {
                    const action = button.dataset.action;
                    const id = button.dataset.id;
                    const rowData = filteredTableData.find(item => item.planNumber === id);
                    console.log('Found rowData:', rowData);
                    
                    if (rowData) {
                        switch (action) {
                            case 'view':
                                console.log('Calling viewDetail with id:', id);
                                viewDetail(id);
                                break;
                            case 'edit':
                                console.log('Calling editItem with id:', id);
                                editItem(id);
                                break;
                            case 'delete':
                                if (confirm(`确定要删除 ${rowData.planProjectName} (${id}) 吗？`)) {
                                    alert(`删除: ${rowData.planProjectName} (${id})`);
                                }
                                break;
                            case 'audit':
                                alert(`审核: ${rowData.planProjectName} (${id})`);
                                break;
                        }
                    }
                } else {
                    console.log('Button not recognized or missing data attributes');
                }
            };

            // Navigation functions are now defined globally above

            // Event Listeners
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    activeTab = tab.dataset.tab;
                    console.log('Active Tab:', activeTab);
                    currentPage = 1;
                    renderTable();
                });
            });

            queryButton.addEventListener('click', () => {
                const formData = new FormData(searchForm);
                const searchParams = {};
                for (let [key, value] of formData.entries()) {
                    searchParams[key] = value;
                }
                console.log('查询条件:', searchParams);
                // 实际项目中这里会根据查询条件过滤数据
                filteredTableData = [...allTableData];
                currentPage = 1;
                renderTable();
            });

            resetButton.addEventListener('click', () => {
                searchForm.reset();
                console.log('重置搜索条件');
                filteredTableData = [...allTableData];
                currentPage = 1;
                renderTable();
            });

            toggleCollapseButton.addEventListener('click', () => {
                const searchArea = document.querySelector('.search-area');
                isCollapsed = !isCollapsed;
                if (isCollapsed) {
                    searchArea.classList.add('collapsed');
                    toggleCollapseButton.textContent = '高级查询';
                } else {
                    searchArea.classList.remove('collapsed');
                    toggleCollapseButton.textContent = '收起';
                }
                console.log('Toggle collapse:', isCollapsed);
            });

            console.log('Binding click event to tableBody:', tableBody);
            tableBody.addEventListener('click', handleAction);
            console.log('Event listener bound successfully');

            pageSizeSelect.addEventListener('change', (event) => {
                pageSize = parseInt(event.target.value);
                currentPage = 1;
                renderTable();
            });

            prevPageButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderTable();
                }
            });

            nextPageButton.addEventListener('click', () => {
                const totalPages = Math.ceil(filteredTableData.length / pageSize);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderTable();
                }
            });

            // Initial render
            renderTable();
        });
    </script>
</body>

</html>