<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 新增/编辑</title>
    <!-- 注意：在路由环境下，CSS文件应在主页面(index.html)中统一加载 -->
    <!-- 如果直接访问此页面，请取消注释以下CSS链接 -->
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/funi-form.css" />
</head>

<body>
    <div id="app" class="form-container">
        <div class="form-step-container">
            <div class="form-step active">
                <div class="step-number">1</div>
                <div class="step-title">招标信息</div>
            </div>
            <iconify-icon icon="ic:outline-navigate-next" class="funi-step-icon"></iconify-icon>
            <div class="form-step">
                <div class="step-number">2</div>
                <div class="step-title">项目信息</div>
            </div>
        </div>
        <div class="container-content">
            <div class="form-step-content form-step-1 active">
                <div class="form-section-title">招标信息</div>
                <div class="funi-form">
                    <form class="form-grid" id="procurementForm">
                        <div class="form-item-row">
                            <label for="planNumber" class="form-item-label">计划编号:</label>
                            <div class="form-item-value">
                                <input type="text" id="planNumber" name="planNumber" value="自动生成" readonly>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="planProjectName" class="form-item-label required">计划项目名称:</label>
                            <div class="form-item-value">
                                <input type="text" id="planProjectName" name="planProjectName" placeholder="请输入计划项目名称" maxlength="100">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementType" class="form-item-label required">采购类型:</label>
                            <div class="form-item-value">
                                <select id="procurementType" name="procurementType">
                                    <option value="">请选择</option>
                                    <option value="施工">施工</option>
                                    <option value="货物">货物</option>
                                    <option value="服务">服务</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingCategory" class="form-item-label required">招标类别:</label>
                            <div class="form-item-value">
                                <select id="biddingCategory" name="biddingCategory">
                                    <option value="">请选择</option>
                                    <option value="工程建设">工程建设</option>
                                    <option value="货物采购">货物采购</option>
                                    <option value="服务采购">服务采购</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementMethod" class="form-item-label required">采购方式:</label>
                            <div class="form-item-value">
                                <select id="procurementMethod" name="procurementMethod">
                                    <option value="">请选择</option>
                                    <option value="公告比选">公告比选</option>
                                    <option value="邀请比选">邀请比选</option>
                                    <option value="竞争性磋商">竞争性磋商</option>
                                    <option value="竞争性谈判">竞争性谈判</option>
                                    <option value="询价择优">询价择优</option>
                                    <option value="单一来源">单一来源</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="budgetAmount" class="form-item-label required">采购预算金额（万元）:</label>
                            <div class="form-item-value">
                                <input type="number" id="budgetAmount" name="budgetAmount" placeholder="请输入采购预算金额" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="fundSource" class="form-item-label required">资金来源:</label>
                            <div class="form-item-value">
                                <select id="fundSource" name="fundSource">
                                    <option value="">请选择</option>
                                    <option value="自有资金">自有资金</option>
                                    <option value="政府投资">政府投资</option>
                                    <option value="其它社会资本">其它社会资本</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingTime" class="form-item-label">招标时间:</label>
                            <div class="form-item-value">
                                <input type="text" id="biddingTime" name="biddingTime" placeholder="请输入招标时间" maxlength="50">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementOrganizationMethod" class="form-item-label required">采购组织方式:</label>
                            <div class="form-item-value">
                                <select id="procurementOrganizationMethod" name="procurementOrganizationMethod">
                                    <option value="">请选择</option>
                                    <option value="委托招标">委托招标</option>
                                    <option value="自主招标">自主招标</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row" id="agencyRow">
                            <label for="agency" class="form-item-label">代理机构:</label>
                            <div class="form-item-value">
                                <select id="agency" name="agency">
                                    <option value="">请选择</option>
                                    <option value="代理机构A">代理机构A</option>
                                    <option value="代理机构B">代理机构B</option>
                                    <option value="代理机构C">代理机构C</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="annualProcurementPlan" class="form-item-label">年采购计划（万元）:</label>
                            <div class="form-item-value">
                                <input type="number" id="annualProcurementPlan" name="annualProcurementPlan" value="0.00" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectHandler" class="form-item-label required">项目经办人:</label>
                            <div class="form-item-value">
                                <input type="text" id="projectHandler" name="projectHandler" value="当前创建人" readonly>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="decisionDate" class="form-item-label required">立项决策日期:</label>
                            <div class="form-item-value">
                                <input type="date" id="decisionDate" name="decisionDate">
                            </div>
                        </div>
                    </form>
                </div>

                <div class="form-section-title">项目信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="projectType" class="form-item-label required">项目类型:</label>
                            <div class="form-item-value">
                                <select id="projectType" name="projectType">
                                    <option value="">请选择</option>
                                    <option value="基建项目">基建项目</option>
                                    <option value="技改项目">技改项目</option>
                                    <option value="维修项目">维修项目</option>
                                    <option value="其他项目">其他项目</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectOwner" class="form-item-label required">项目业主:</label>
                            <div class="form-item-value">
                                <input type="text" id="projectOwner" name="projectOwner" placeholder="请输入项目业主" maxlength="100">
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="projectBasicInfo" class="form-item-label">项目基本情况:</label>
                            <div class="form-item-value">
                                <textarea id="projectBasicInfo" name="projectBasicInfo" placeholder="请输入项目基本情况" rows="4" maxlength="500"></textarea>
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="procurementContent" class="form-item-label">采购内容:</label>
                            <div class="form-item-value">
                                <textarea id="procurementContent" name="procurementContent" placeholder="请输入采购内容" rows="4" maxlength="500"></textarea>
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="technicalRequirements" class="form-item-label">技术要求:</label>
                            <div class="form-item-value">
                                <textarea id="technicalRequirements" name="technicalRequirements" placeholder="请输入技术要求" rows="4" maxlength="500"></textarea>
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="remarks" class="form-item-label">备注:</label>
                            <div class="form-item-value">
                                <textarea id="remarks" name="remarks" placeholder="请输入备注信息" rows="3" maxlength="300"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="form-actions">
            <button type="button" class="button" onclick="goBack()">返回</button>
            <button type="button" class="button" onclick="saveDraft()">保存草稿</button>
            <button type="button" class="button primary" onclick="submitForm()">提交</button>
        </div>
    </div>

    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            const isEdit = !!id;

            // 更新页面标题
            document.title = isEdit ? '采购计划管理 - 编辑' : '采购计划管理 - 新增';

            // 采购组织方式变化时控制代理机构显示
            const procurementOrganizationMethod = document.getElementById('procurementOrganizationMethod');
            const agencyRow = document.getElementById('agencyRow');
            const agencySelect = document.getElementById('agency');

            procurementOrganizationMethod.addEventListener('change', function() {
                if (this.value === '委托招标') {
                    agencyRow.style.display = 'flex';
                    agencySelect.setAttribute('required', 'required');
                    agencyRow.querySelector('.form-item-label').classList.add('required');
                } else {
                    agencyRow.style.display = 'none';
                    agencySelect.removeAttribute('required');
                    agencyRow.querySelector('.form-item-label').classList.remove('required');
                    agencySelect.value = '';
                }
            });

            // 初始化代理机构显示状态
            if (procurementOrganizationMethod.value !== '委托招标') {
                agencyRow.style.display = 'none';
            }

            // 如果是编辑模式，加载数据
            if (isEdit) {
                loadFormData(id);
            }

            // 表单验证
            function validateForm() {
                const requiredFields = [
                    { id: 'planProjectName', name: '计划项目名称' },
                    { id: 'procurementType', name: '采购类型' },
                    { id: 'biddingCategory', name: '招标类别' },
                    { id: 'procurementMethod', name: '采购方式' },
                    { id: 'budgetAmount', name: '采购预算金额' },
                    { id: 'fundSource', name: '资金来源' },
                    { id: 'procurementOrganizationMethod', name: '采购组织方式' },
                    { id: 'projectHandler', name: '项目经办人' },
                    { id: 'decisionDate', name: '立项决策日期' },
                    { id: 'projectType', name: '项目类型' },
                    { id: 'projectOwner', name: '项目业主' }
                ];

                for (let field of requiredFields) {
                    const element = document.getElementById(field.id);
                    if (!element.value.trim()) {
                        alert(`请填写${field.name}`);
                        element.focus();
                        return false;
                    }
                }

                // 如果选择委托招标，检查代理机构
                if (procurementOrganizationMethod.value === '委托招标' && !agencySelect.value) {
                    alert('请选择代理机构');
                    agencySelect.focus();
                    return false;
                }

                // 验证预算金额
                const budgetAmount = document.getElementById('budgetAmount').value;
                if (budgetAmount && (isNaN(budgetAmount) || parseFloat(budgetAmount) <= 0)) {
                    alert('采购预算金额必须大于0');
                    document.getElementById('budgetAmount').focus();
                    return false;
                }

                return true;
            }

            // 加载表单数据（编辑模式）
            function loadFormData(id) {
                // 模拟数据加载
                const mockData = {
                    planNumber: id,
                    planProjectName: '办公楼装修改造工程',
                    procurementType: '施工',
                    biddingCategory: '工程建设',
                    procurementMethod: '公告比选',
                    budgetAmount: '500.00',
                    fundSource: '自有资金',
                    biddingTime: '2024年3月',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构A',
                    annualProcurementPlan: '500.00',
                    projectHandler: '张三',
                    decisionDate: '2024-01-15',
                    projectType: '基建项目',
                    projectOwner: '集团本部',
                    projectBasicInfo: '对办公楼进行全面装修改造，提升办公环境',
                    procurementContent: '包括装修材料、施工服务等',
                    technicalRequirements: '符合国家建筑装修标准',
                    remarks: '项目紧急，需要加快进度'
                };

                // 填充表单数据
                Object.keys(mockData).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = mockData[key];
                    }
                });

                // 触发采购组织方式变化事件
                procurementOrganizationMethod.dispatchEvent(new Event('change'));
            }

            // 全局函数
            window.goBack = function() {
                window.top.location.hash = '#/procurement-plan';
            };

            window.saveDraft = function() {
                const formData = new FormData(document.getElementById('procurementForm'));
                console.log('保存草稿:', Object.fromEntries(formData));
                alert('草稿保存成功！');
            };

            window.submitForm = function() {
                if (!validateForm()) {
                    return;
                }

                const formData = new FormData(document.getElementById('procurementForm'));
                const data = Object.fromEntries(formData);
                
                // 添加项目信息
                data.projectType = document.getElementById('projectType').value;
                data.projectOwner = document.getElementById('projectOwner').value;
                data.projectBasicInfo = document.getElementById('projectBasicInfo').value;
                data.procurementContent = document.getElementById('procurementContent').value;
                data.technicalRequirements = document.getElementById('technicalRequirements').value;
                data.remarks = document.getElementById('remarks').value;

                console.log('提交数据:', data);
                
                if (isEdit) {
                    alert('修改成功！');
                } else {
                    alert('新增成功！');
                }
                
                // 返回列表页
                window.top.location.hash = '#/procurement-plan';
            };
        });
    </script>
</body>

</html>