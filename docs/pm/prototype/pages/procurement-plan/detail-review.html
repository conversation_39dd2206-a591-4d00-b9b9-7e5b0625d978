<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 详情</title>
    <!-- 注意：在路由环境下，CSS文件应在主页面(index.html)中统一加载 -->
    <!-- 如果直接访问此页面，请取消注释以下CSS链接 -->
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/funi-detail.css" />
</head>

<body>
    <div id="app" class="detail-container">
        <!-- 审批信息头部 -->
        <div class="approval-header">
            <div class="approval-info">
                <div class="approval-item">
                    <span class="approval-label">审核状态:</span>
                    <span class="approval-value status-pending" id="auditStatus">待审核</span>
                </div>
                <div class="approval-item">
                    <span class="approval-label">当前审核人:</span>
                    <span class="approval-value" id="currentAuditor">李四</span>
                </div>
                <div class="approval-item">
                    <span class="approval-label">创建时间:</span>
                    <span class="approval-value" id="createTime">2024-01-15 10:30:00</span>
                </div>
            </div>
            <div class="approval-actions" id="approvalActions">
                <button type="button" class="button" onclick="auditAction('reject')">驳回</button>
                <button type="button" class="button primary" onclick="auditAction('approve')">通过</button>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="tab-container">
            <div class="tab-nav">
                <div class="tab-item active" data-tab="basic-info">基本信息</div>
                <div class="tab-item" data-tab="process-records">流程记录</div>
            </div>
        </div>

        <!-- 基本信息标签页 -->
        <div class="tab-content active" id="basic-info">
            <div class="form-section-title">招标信息</div>
            <div class="funi-form">
                <div class="form-grid">
                    <div class="form-item-row">
                        <label class="form-item-label">计划编号:</label>
                        <div class="form-item-value" id="planNumber">CGGH-2024-001</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">计划项目名称:</label>
                        <div class="form-item-value" id="planProjectName">办公楼装修改造工程</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">采购类型:</label>
                        <div class="form-item-value" id="procurementType">施工</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">招标类别:</label>
                        <div class="form-item-value" id="biddingCategory">工程建设</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">采购方式:</label>
                        <div class="form-item-value" id="procurementMethod">公告比选</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">采购预算金额（万元）:</label>
                        <div class="form-item-value" id="budgetAmount">500.00</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">资金来源:</label>
                        <div class="form-item-value" id="fundSource">自有资金</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">招标时间:</label>
                        <div class="form-item-value" id="biddingTime">2024年3月</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">采购组织方式:</label>
                        <div class="form-item-value" id="procurementOrganizationMethod">委托招标</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">代理机构:</label>
                        <div class="form-item-value" id="agency">代理机构A</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">年采购计划（万元）:</label>
                        <div class="form-item-value" id="annualProcurementPlan">500.00</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">项目经办人:</label>
                        <div class="form-item-value" id="projectHandler">张三</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">立项决策日期:</label>
                        <div class="form-item-value" id="decisionDate">2024-01-15</div>
                    </div>
                </div>
            </div>

            <div class="form-section-title">项目信息</div>
            <div class="funi-form">
                <div class="form-grid">
                    <div class="form-item-row">
                        <label class="form-item-label">项目类型:</label>
                        <div class="form-item-value" id="projectType">基建项目</div>
                    </div>
                    <div class="form-item-row">
                        <label class="form-item-label">项目业主:</label>
                        <div class="form-item-value" id="projectOwner">集团本部</div>
                    </div>
                    <div class="form-item-row form-item-full-width">
                        <label class="form-item-label">项目基本情况:</label>
                        <div class="form-item-value" id="projectBasicInfo">对办公楼进行全面装修改造，提升办公环境，包括墙面粉刷、地面铺设、电路改造等工程内容。</div>
                    </div>
                    <div class="form-item-row form-item-full-width">
                        <label class="form-item-label">采购内容:</label>
                        <div class="form-item-value" id="procurementContent">包括装修材料采购、施工服务、监理服务等，涵盖整个装修改造工程的全部内容。</div>
                    </div>
                    <div class="form-item-row form-item-full-width">
                        <label class="form-item-label">技术要求:</label>
                        <div class="form-item-value" id="technicalRequirements">符合国家建筑装修标准，使用环保材料，确保施工质量和安全，工期不超过3个月。</div>
                    </div>
                    <div class="form-item-row form-item-full-width">
                        <label class="form-item-label">备注:</label>
                        <div class="form-item-value" id="remarks">项目紧急，需要加快进度，请优先安排审核。</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 流程记录标签页 -->
        <div class="tab-content" id="process-records">
            <div class="process-timeline">
                <div class="timeline-item completed">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <span class="timeline-title">创建申请</span>
                            <span class="timeline-time">2024-01-15 10:30:00</span>
                        </div>
                        <div class="timeline-body">
                            <div class="timeline-info">
                                <span class="info-label">操作人:</span>
                                <span class="info-value">张三</span>
                            </div>
                            <div class="timeline-info">
                                <span class="info-label">操作:</span>
                                <span class="info-value">提交采购计划申请</span>
                            </div>
                            <div class="timeline-info">
                                <span class="info-label">备注:</span>
                                <span class="info-value">办公楼装修改造工程采购计划，预算500万元</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="timeline-item completed">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <span class="timeline-title">部门审核</span>
                            <span class="timeline-time">2024-01-16 09:15:00</span>
                        </div>
                        <div class="timeline-body">
                            <div class="timeline-info">
                                <span class="info-label">操作人:</span>
                                <span class="info-value">王五</span>
                            </div>
                            <div class="timeline-info">
                                <span class="info-label">操作:</span>
                                <span class="info-value">部门审核通过</span>
                            </div>
                            <div class="timeline-info">
                                <span class="info-label">备注:</span>
                                <span class="info-value">项目符合部门规划，同意进入下一审核环节</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="timeline-item current">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <span class="timeline-title">领导审批</span>
                            <span class="timeline-time">待处理</span>
                        </div>
                        <div class="timeline-body">
                            <div class="timeline-info">
                                <span class="info-label">当前审核人:</span>
                                <span class="info-value">李四</span>
                            </div>
                            <div class="timeline-info">
                                <span class="info-label">状态:</span>
                                <span class="info-value">待审核</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="timeline-item pending">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <span class="timeline-title">最终审批</span>
                            <span class="timeline-time">待处理</span>
                        </div>
                        <div class="timeline-body">
                            <div class="timeline-info">
                                <span class="info-label">审核人:</span>
                                <span class="info-value">赵六</span>
                            </div>
                            <div class="timeline-info">
                                <span class="info-label">状态:</span>
                                <span class="info-value">等待中</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="form-actions">
            <button type="button" class="button" onclick="goBack()">返回</button>
            <button type="button" class="button" onclick="editRecord()" id="editBtn">编辑</button>
            <button type="button" class="button" onclick="printRecord()">打印</button>
        </div>
    </div>

    <!-- 审核意见弹窗 -->
    <div id="auditModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="auditModalTitle">审核意见</h3>
                <span class="modal-close" onclick="closeAuditModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-item-row">
                    <label class="form-item-label">审核结果:</label>
                    <div class="form-item-value">
                        <select id="auditResult">
                            <option value="approve">通过</option>
                            <option value="reject">驳回</option>
                        </select>
                    </div>
                </div>
                <div class="form-item-row form-item-full-width">
                    <label class="form-item-label">审核意见:</label>
                    <div class="form-item-value">
                        <textarea id="auditComment" placeholder="请输入审核意见" rows="4" maxlength="500"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="button" onclick="closeAuditModal()">取消</button>
                <button type="button" class="button primary" onclick="submitAudit()">确定</button>
            </div>
        </div>
    </div>

    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            const mode = urlParams.get('mode') || 'view'; // view, audit

            // 根据模式调整页面
            if (mode === 'audit') {
                document.title = '采购计划管理 - 审核';
                document.querySelector('.approval-actions').style.display = 'flex';
            } else {
                document.title = '采购计划管理 - 详情';
                document.querySelector('.approval-actions').style.display = 'none';
            }

            // 标签页切换
            const tabItems = document.querySelectorAll('.tab-item');
            const tabContents = document.querySelectorAll('.tab-content');

            tabItems.forEach(item => {
                item.addEventListener('click', () => {
                    const tabId = item.getAttribute('data-tab');
                    
                    // 移除所有活动状态
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    
                    // 添加当前活动状态
                    item.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // 加载详情数据
            if (id) {
                loadDetailData(id);
            }

            // 加载详情数据（模拟）
            function loadDetailData(id) {
                // 这里应该从后端API获取数据
                const mockData = {
                    planNumber: id,
                    planProjectName: '办公楼装修改造工程',
                    procurementType: '施工',
                    biddingCategory: '工程建设',
                    procurementMethod: '公告比选',
                    budgetAmount: '500.00',
                    fundSource: '自有资金',
                    biddingTime: '2024年3月',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构A',
                    annualProcurementPlan: '500.00',
                    projectHandler: '张三',
                    decisionDate: '2024-01-15',
                    projectType: '基建项目',
                    projectOwner: '集团本部',
                    projectBasicInfo: '对办公楼进行全面装修改造，提升办公环境，包括墙面粉刷、地面铺设、电路改造等工程内容。',
                    procurementContent: '包括装修材料采购、施工服务、监理服务等，涵盖整个装修改造工程的全部内容。',
                    technicalRequirements: '符合国家建筑装修标准，使用环保材料，确保施工质量和安全，工期不超过3个月。',
                    remarks: '项目紧急，需要加快进度，请优先安排审核。',
                    auditStatus: '待审核',
                    currentAuditor: '李四',
                    createTime: '2024-01-15 10:30:00'
                };

                // 填充数据到页面
                Object.keys(mockData).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        element.textContent = mockData[key];
                    }
                });

                // 设置审核状态样式
                const statusElement = document.getElementById('auditStatus');
                statusElement.className = `approval-value status-${getStatusClass(mockData.auditStatus)}`;
            }

            function getStatusClass(status) {
                const statusMap = {
                    '待审核': 'pending',
                    '审核中': 'processing',
                    '已通过': 'approved',
                    '已驳回': 'rejected'
                };
                return statusMap[status] || 'pending';
            }

            // 全局函数
            window.goBack = function() {
                window.top.location.hash = '#/procurement-plan';
            };

            window.editRecord = function() {
                const id = new URLSearchParams(window.location.search).get('id');
                window.top.location.hash = `#/procurement-plan/add-edit?id=${id}`;
            };

            window.printRecord = function() {
                window.print();
            };

            window.auditAction = function(action) {
                const modal = document.getElementById('auditModal');
                const title = document.getElementById('auditModalTitle');
                const result = document.getElementById('auditResult');
                
                if (action === 'approve') {
                    title.textContent = '审核通过';
                    result.value = 'approve';
                } else {
                    title.textContent = '审核驳回';
                    result.value = 'reject';
                }
                
                modal.style.display = 'flex';
            };

            window.closeAuditModal = function() {
                document.getElementById('auditModal').style.display = 'none';
                document.getElementById('auditComment').value = '';
            };

            window.submitAudit = function() {
                const result = document.getElementById('auditResult').value;
                const comment = document.getElementById('auditComment').value;
                
                if (!comment.trim()) {
                    alert('请输入审核意见');
                    return;
                }
                
                console.log('审核结果:', { result, comment });
                
                if (result === 'approve') {
                    alert('审核通过成功！');
                } else {
                    alert('审核驳回成功！');
                }
                
                closeAuditModal();
                window.top.location.hash = '#/procurement-plan';
            };

            // 点击模态框外部关闭
            window.onclick = function(event) {
                const modal = document.getElementById('auditModal');
                if (event.target === modal) {
                    closeAuditModal();
                }
            };
        });
    </script>
</body>

</html>