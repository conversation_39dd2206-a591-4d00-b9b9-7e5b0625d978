# 生成Select提示词

请根据以下要求生成Select组件：

1.  **单选属性**: **所有生成的 `select` 元素都必须是单选，即绝对不能包含 `multiple` 属性。**
2.  **多选提示（仅提示，不实现多选功能）**: 如果某个 `select` 字段在需求上被描述为“多选”，则必须在对应的 `input-and-tip-wrapper` 中添加一个 `div` 元素，并使用 `form-item-tip` 类来提供多选提示，但 `select` 元素本身仍为单选。
    - 示例：
    ```html
    <div class="input-and-tip-wrapper">
      <select id="mySelect" name="mySelect">
        <!-- options -->
      </select>
      <div class="form-item-tip">此为单选字段，如需多选请联系管理员</div>
    </div>
