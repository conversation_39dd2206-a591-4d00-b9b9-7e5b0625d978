## 生成Funi管理系统页面提示词

### 任务概述

根据业务需求描述，生成完整的页面文件（HTML），包括列表页面、新增/编辑页面和详情/审核页面。页面需要与现有的路由系统完全兼容，确保路径一致性。

**指令格式**: `生成页面-[模块名称或菜单名称]`

**生成范围**:
- **列表页面**: `list.html` - 数据展示、搜索、分页
- **新增/编辑页面**: `add-edit.html` - 表单录入、数据编辑  
- **详情/审核页面**: `detail-review.html` - 数据查看、审核操作

**文件位置**: 所有页面文件生成在`docs/pm/prototype/pages/`目录下，目录结构需要与现有路由系统保持一致。

### 执行流程（必须按顺序执行）

#### 步骤1：路由配置检查
- **查看配置文件**: 检查 `docs/pm/prototype/index.html` 文件
- **提取路由信息**: 找到目标菜单的实际路由配置（href属性值）
- **确认basePath**: 从href值中提取basePath（去掉#/前缀）
- **验证存在性**: 确认目标菜单在主导航配置中存在
- **示例**: href="#/procurement-plan" → basePath为 "procurement-plan"
- **停止条件**: 如果找不到对应菜单，立即停止执行并报告错误

#### 步骤2：模板文件验证
- **检查模板目录**: 查看 `docs/pm/tools/framework/templates/` 目录
- **确认模板文件**: 验证以下模板文件是否存在：
  - `list-page-template.html`
  - `form-page-template.html` 
  - `detail-page-template.html`
- **停止条件**: 如果模板文件不存在，立即停止执行并报告错误

#### 步骤3：页面生成
- **构建目标路径**: 根据步骤1的basePath构建页面文件路径
- **目录结构**: `docs/pm/prototype/pages/{basePath}/`
- **文件命名**: 使用固定文件名（list.html, add-edit.html, detail-review.html）
- **生成页面**: 基于模板生成对应的页面文件

#### 步骤4：验证访问
- **路径验证**: 确保生成的文件路径与路由配置完全匹配
- **功能测试**: 通过浏览器访问页面并确认功能正常
- **错误检查**: 确保浏览器控制台无JavaScript错误

### 页面生成规范

#### 路由一致性要求
- **严格匹配**: 页面目录路径必须与菜单路由配置完全对应
- **路由解析**: 路由器通过 `pages/{basePath}/{pageFileName}` 加载页面
- **页面路径规则**:
  - 列表页面: `#{basePath}` → `pages/{basePath}/list.html`
  - 新增/编辑页面: `#{basePath}/add-edit` → `pages/{basePath}/add-edit.html`
  - 详情/审核页面: `#{basePath}/detail-review?review=1` → `pages/{basePath}/detail-review.html`

#### 文件命名规则
- **目录名**: 使用从主导航配置中提取的 `basePath`
- **文件名**: 固定为 `list.html`、`add-edit.html`、`detail-review.html`
- **路径示例**: `docs/pm/prototype/pages/{basePath}/list.html`

#### 通用要求
- **原型性质**: 生成的页面仅为原型，展示结构和基本功能
- **技术栈**: 使用原生HTML、CSS和JavaScript，禁止使用前端框架
- **页面结构**: 独立HTML文件，不包含整体布局，在iframe中加载
- **主体容器**: 使用 `<div id="app" class="container">` 作为主容器
- **资源路径**: CSS和JS文件相对路径为 `../assets/`（相对于templates子目录）
- **CSS引用**: 必须引用CSS文件，不能注释掉，确保页面样式正常显示
- **页面跳转**: 使用 `window.top.location.hash` 并严格遵循主导航配置中的路径

#### 页面类型详细要求

**1. 列表页面** (`list.html`):
- **模板**: 使用 `docs/pm/tools/framework/templates/list-page-template.html`
- **操作列要求**: 必须包含"详情"按钮，调用viewDetail函数跳转到详情页面
- **按钮顺序**: 详情、编辑、删除、审核（如适用）
- **额外样式**: 引用 `funi-list.css`

**2. 新增/编辑页面** (`add-edit.html`):
- **模板**: 使用 `docs/pm/tools/framework/templates/form-page-template.html`
- **表单页面**: 支持数据录入和编辑
- **额外样式**: 引用 `funi-form.css`

**3. 详情/审核页面** (`detail-review.html`):
- **模板**: 使用 `docs/pm/tools/framework/templates/detail-page-template.html`
- **只读展示**: 支持审核操作
- **tab切换要求**: 必须使用iframe兼容的JavaScript初始化模式

#### URL参数获取
在页面中获取URL参数时，使用以下代码模式：
```javascript
const hash = window.top.location.hash;
const queryString = hash.split('?')[1] || '';
const urlParams = new URLSearchParams(queryString);
const id = urlParams.get('id');
```

### 检查与验证

#### 执行前检查
- **停止执行条件**: 如果以下任一条件不满足，必须立即停止执行：
  1. 无法在index.html中找到目标菜单的路由配置
  2. 模板文件不存在或无法访问
  3. 目标目录无法创建或访问
- **错误报告**: 遇到停止条件时，必须明确报告具体的错误原因
- **禁止推测**: 严禁基于推测或假设生成页面文件路径

#### 路由一致性检查
- **路径匹配**: 验证生成的页面目录路径与主导航配置完全一致
- **跳转链接**: 检查所有 `window.top.location.hash` 跳转使用正确的basePath
- **文件位置**: 确认页面文件创建在正确的目录结构下
- **内部导航**: 验证页面间跳转链接（新增、编辑、详情、返回）路径正确
- **参数传递**: 确保详情页面的参数格式符合路由规范
- **访问测试**: 生成完成后必须验证页面是否可以通过正确的URL访问

#### 表单页面检查
- **第一步active类**: 确保第一个 `.form-step` 和 `.form-step-content` 都有 `active` 类
- **步骤内容对应**: 确保每个 `.form-step-content` 都有对应的 `form-step-X` 类名
- **按钮区域**: 确保 `.form-actions` 有 `id="formActions"` 属性
- **静态按钮禁止**: 严格检查HTML中不能包含任何静态的导航按钮，必须由JavaScript动态生成
- **按钮区域清空**: 确保 `formActions` 容器在HTML中为空，所有按钮由updateButtons函数动态填充

#### 资源引用检查
- **CSS和JS路径**: 检查所有CSS和JS文件的相对路径是否正确
- **select标签**: 禁止使用 `multiple` 属性，如需多选在 `<option value="">` 中添加"(多选)"提示

#### JavaScript错误检查
- **函数作用域**: 确保在HTML中直接调用的函数定义在全局作用域（window对象上）
- **CSS类名一致性**: 确保JavaScript中使用的CSS选择器与HTML结构中的实际类名完全匹配
- **iframe兼容性**: 确保JavaScript初始化逻辑兼容iframe环境，使用多种初始化时机
- **重复初始化检查**: 确保初始化函数不会被重复调用，使用标志变量或DOM状态检查防重复：
  ```javascript
  // 使用标志变量防重复
  let isInitialized = false;
  function initPageData() {
      if (isInitialized) return;
      isInitialized = true;
      // 初始化逻辑...
  }
  
  // 或检查DOM状态防重复
  function initPageData() {
      if (document.querySelector('.form-actions')) return;
      // 初始化逻辑...
  }
  ```
- **DOM重复创建检查**: 在动态创建DOM元素前必须检查元素是否已存在：
  ```javascript
  // 正确示例 - 防重复创建
  const existingFormActions = document.querySelector('.form-actions');
  if (!existingFormActions) {
      const formActions = document.createElement('div');
      formActions.className = 'form-actions';
      mainContent.appendChild(formActions);
  }
  ```

### 技术约束与限制

#### 基础约束
- **禁止使用**: 前端框架（Vue、React）、自定义组件（funi-开头）
- **必须使用**: 原生HTML、CSS、JavaScript
- **页面跳转**: 使用 `window.top.location.hash` 修改顶层窗口hash值
- **路由一致性**: 所有路径必须严格遵循主导航配置中定义的路由结构
- **样式一致性**: 严格遵循模板的样式结构和类名
- **兼容性**: 确保在iframe环境中稳定工作

#### 防重复机制
- **防重复初始化**: 确保每个初始化函数在页面生命周期中只被调用一次
- **DOM创建前检查**: 在动态创建任何DOM元素前，必须先检查该元素是否已存在

### 成功标准

#### 执行要点
1. **严格按顺序执行**: 查看index.html → 验证模板 → 生成文件 → 验证访问
2. **零容忍推测**: 所有路径信息必须从实际配置文件中获取，严禁推测
3. **立即验证**: 每个步骤完成后立即验证结果，发现问题立即停止

#### 验证标准
- ✅ 页面文件生成在正确的目录结构下
- ✅ 所有资源文件路径正确，页面样式正常显示
- ✅ 页面可以通过正确的URL路径访问
- ✅ 页面间跳转链接工作正常
- ✅ 浏览器控制台无JavaScript错误

**最终验证**: 生成完成后，必须通过浏览器访问页面并确认所有功能正常工作。
